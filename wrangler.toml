name = "kwanso_api"
main = "src/index.ts"
compatibility_date = "2025-01-29"

#Deploys to the Tech Help Account
account_id = "5d44ab7e1f6c6ab64f37939e9b998c0e" 

# compatibility_flags = [ "nodejs_compat" ]

# [vars]
# MY_VAR = "my-variable"

# [[kv_namespaces]]
# binding = "MY_KV_NAMESPACE"
# id = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# [[r2_buckets]]
# binding = "MY_BUCKET"
# bucket_name = "my-bucket"

# [[d1_databases]]
# binding = "DB"
# database_name = "my-database"
# database_id = ""

# [ai]
# binding = "AI"

# [observability]
# enabled = true
# head_sampling_rate = 1