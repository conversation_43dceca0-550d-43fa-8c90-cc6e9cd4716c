export interface Booking {
    id: string;
    user_id: string;
    schedule_id: string;
    payment_ref?: string;
}

export enum BookingStatus {
    PENDING = 'pending',
    CONFIRMED = 'confirmed',
    CANCELLED = 'cancelled',
    COMPLETED = 'completed'
}

export interface CreateBookingDto {
    schedule_id: string;
    payment_ref?: string;
}

export interface UpdateBookingDto {
    status?: BookingStatus;
    payment_id?: string;
}