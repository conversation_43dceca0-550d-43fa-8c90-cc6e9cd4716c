import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import { BookingService } from './service/booking.service';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';
import { BadRequestException } from '../../core/exception';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const bookingRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
bookingRoute.use('*', authMiddleware);

// Supabase middleware
bookingRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Create a new booking
bookingRoute.post('/', async (c: Context) => {
    const bookingService = new BookingService(c.get('supabase'));
    const user = c.get('user');
    const data = await c.req.json();

    console.log(data)

    // Validate required fields
    if (!data.schedule_id) {
        throw new BadRequestException('Schedule ID is required');
    }

    const booking = await bookingService.create(user.id, data);
    return c.json({
        status: 'success',
        data: booking,
        message: 'Booking created successfully'
    }, 201);
});

// Get all bookings
bookingRoute.get('/', async (c: Context) => {
    const bookingService = new BookingService(c.get('supabase'));
    const user = c.get('user');

    // If user is not admin, only show their bookings
    const bookings = await bookingService.findAll(user.id);

    return c.json(bookings.map((booking:any) => ({
        id: booking.id,
        status: booking.status,
        partner_avatar_url : "https://cbbstwltufvzpsqvnahz.supabase.co/storage/v1/object/public/avatars/public/logoipsum.png",
        number_of_tickets : booking.number_of_tickets,
        total_amount : booking.total_amount,
        booking_reference : booking.booking_reference,
        arrival_time : booking.schedule_id.arrival_time,
        departure_time : booking.schedule_id.departure_time,
        route : {
            origin : booking.schedule_id.route_id.origin,
            destination : booking.schedule_id.route_id.destination
        },
        pickup_stop :booking.pickup_stop.location_id,
        drop_off_stop : booking.drop_off_stop.location_id,
        booking_schedule_date : booking.booking_schedule_date,
    })) );
});

// Get a specific booking
bookingRoute.get('/:id', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const bookingService = new BookingService(c.get('supabase'));
    const booking = await bookingService.findOne(c.req.param('id'));

    // Check if user has access to this booking
    const user = c.get('user');
    if (user.role === Role.USER && booking.user_id !== user.id) {
        return c.json({ message: 'Booking not found' }, 404);
    }

    return c.json({
        status: 'success',
        data: booking
    });
});

// Update a booking
bookingRoute.put('/:id', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const bookingService = new BookingService(c.get('supabase'));
    const bookingId = c.req.param('id');
    const user = c.get('user');

    // Check if user has access to this booking
    const existingBooking = await bookingService.findOne(bookingId);
    if (user.role === Role.USER && existingBooking.user_id !== user.id) {
        return c.json({ message: 'Booking not found' }, 404);
    }

    const data = await c.req.json();
    const booking = await bookingService.update(bookingId, data);

    return c.json({
        status: 'success',
        data: booking,
        message: 'Booking updated successfully'
    });
});

// Delete a booking
bookingRoute.delete('/:id', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const bookingService = new BookingService(c.get('supabase'));
    const bookingId = c.req.param('id');
    const user = c.get('user');

    // Check if user has access to this booking
    const existingBooking = await bookingService.findOne(bookingId);
    if (user.role === Role.USER && existingBooking.user_id !== user.id) {
        return c.json({ message: 'Booking not found' }, 404);
    }

    await bookingService.delete(bookingId);
    return c.json({
        status: 'success',
        message: 'Booking deleted successfully'
    });
});

export default bookingRoute;