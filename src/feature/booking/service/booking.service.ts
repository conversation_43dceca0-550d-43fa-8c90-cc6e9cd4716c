import { SupabaseClient } from '@supabase/supabase-js';
import { Booking, CreateBookingDto, UpdateBookingDto, BookingStatus } from '../interface/booking.interface';
import { BadRequestException } from '../../../core/exception';

export class BookingService {
    constructor(private readonly supabase: SupabaseClient) {}

    async create(userId: string, createBookingDto: CreateBookingDto): Promise<Booking> {
        const { data, error } = await this.supabase
            .from('bookings')
            .insert([{
                user_id: userId,
                schedule_id: createBookingDto.schedule_id,
                payment_ref: createBookingDto.payment_ref,
                status: "Success",
                pickup_stop : 4,
                drop_off_stop : 8,
                total_amount : 525,
                number_of_tickets : 5,
                booking_reference : `BK_${Date.now()}`,
                booking_schedule_date : new Date().toISOString(),
            }])
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async findAll(userId?: string): Promise<Booking[]> {
        let query = this.supabase
            .from('bookings')
            .select(`
                *,
                pickup_stop(location_id(id,name)),
                drop_off_stop(location_id(id,name)),
                schedule_id(*, route_id(origin(id,name), destination(id,name)))
                `)
            .order('created_at', { ascending: false });

        if (userId) {
            query = query.eq('user_id', userId);
        }

        const { data, error } = await query;

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }

    async findOne(id: string): Promise<Booking> {
        const { data, error } = await this.supabase
            .from('bookings')
            .select('*, schedule:schedule_id(*), user:user_id(*)')
            .eq('id', id)
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async update(id: string, updateBookingDto: UpdateBookingDto): Promise<Booking> {
        const { data, error } = await this.supabase
            .from('bookings')
            .update({
                ...updateBookingDto,
                updated_at: new Date().toISOString()
            })
            .eq('id', id)
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async delete(id: string): Promise<void> {
        const { error } = await this.supabase
            .from('bookings')
            .delete()
            .eq('id', id);

        if (error) {
            throw new BadRequestException(error.message);
        }
    }
}