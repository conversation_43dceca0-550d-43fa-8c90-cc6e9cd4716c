import { Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import { PartnerService } from './service/partner.service';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';

const partnerRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
partnerRoute.use('*', authMiddleware);

// Create a new partner (Admin only)
partnerRoute.post('/', roleGuard([Role.ADMIN]), async (c) => {
    const partnerService = new PartnerService(c.get('supabase'));
    const data = await c.req.json();
    const partner = await partnerService.create(data);
    return c.json(partner, 201);
});

// Get all partners (Admin only)
partnerRoute.get('/', roleGuard([Role.ADMIN]), async (c) => {
    const partnerService = new PartnerService(c.get('supabase'));
    const partners = await partnerService.findAll();
    return c.json(partners);
});

// Get a specific partner (Admin only)
partnerRoute.get('/:id', roleGuard([Role.ADMIN]), async (c) => {
    const partnerService = new PartnerService(c.get('supabase'));
    const partner = await partnerService.findOne(c.req.param('id'));
    return c.json(partner);
});

// Update a partner (Admin only)
partnerRoute.put('/:id', roleGuard([Role.ADMIN]), async (c) => {
    const partnerService = new PartnerService(c.get('supabase'));
    const data = await c.req.json();
    const partner = await partnerService.update(c.req.param('id'), data);
    return c.json(partner);
});

// Delete a partner (Admin only)
partnerRoute.delete('/:id', roleGuard([Role.ADMIN]), async (c) => {
    const partnerService = new PartnerService(c.get('supabase'));
    await partnerService.delete(c.req.param('id'));
    return c.json({ message: 'Partner deleted successfully' }, 200);
});

export default partnerRoute;