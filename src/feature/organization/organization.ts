import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import authMiddleware from '../auth/middleware/auth.middleware';
import { BadRequestException } from '../../core/exception';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const organizationRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
organizationRoute.use('*', authMiddleware);

// Supabase middleware
organizationRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Get all stats for the dashboard
organizationRoute.get('/stats', async (c: Context) => {

    const user = c.get('user');

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        //FIXME: IMPLEMENT ME FULLY
        await new Promise(resolve => setTimeout(resolve, 2000));

        return c.json({
            totalRevenue: 542,
            completedBookings: 45,
            activeRoutes: 20,
            todayBookings: 10
        });
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

//Get business earnings performance trend
organizationRoute.get('/performance/earnings/trend', async (c: Context) => {

    const user = c.get('user');

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        //FIXME: IMPLEMENT ME FULLY
        await new Promise(resolve => setTimeout(resolve, 2000));

        const trend = Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - 29 + i);

            return { x: date, y: Math.floor(Math.random() * 5000) + 2000 };
        });

        return c.json({
            trend,
            stats : {
                total : Math.floor(Math.random() * 5000) + 7000,
                average : Math.floor(Math.random() * 1000) + 700,
                peak : Math.floor(Math.random() * 9000) + 4000,
                growth : Math.floor(Math.random() * 100)
            }
        });
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

//Get business bookings performance trend
organizationRoute.get('/performance/bookings/trend', async (c: Context) => {

    const user = c.get('user');

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        //FIXME: IMPLEMENT ME FULLY
        await new Promise(resolve => setTimeout(resolve, 2000));

        const trend = Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - 29 + i);

            return { x: date, y: Math.floor(Math.random() * 100) + 50 };
        });

        return c.json({
            trend,
             stats : {
                total : Math.floor(Math.random() * 5000) + 7000,
                average : Math.floor(Math.random() * 1000) + 700,
                peak : Math.floor(Math.random() * 9000) + 4000,
                growth : Math.floor(Math.random() * 100)
            }
        });
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});


export interface ILocation {
    name: string;
    address: string;
    lat: number;
    lng: number;
}

export interface IBooking {
    id: string;
    status: string;
    rider: {
        id: string;
        first_name: string;
        last_name: string;
        avatar_url?: string;
    };
    partner_avatar_url?: string;
    number_of_tickets: number;
    total_amount: number;
    booking_reference: string;
    arrival_time: string;
    departure_time: string;
    route: {
        origin: ILocation;
        destination: ILocation;
    };
    pickup_stop: ILocation;
    drop_off_stop: ILocation;
    booking_schedule_date: string;
}

// Get the summary of recent bookings
organizationRoute.get('/bookings/recent', async (c: Context) => {
    const user = c.get('user');

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        // Validate query parameters
        if (filters.start_date && isNaN(Date.parse(filters.start_date))) {
            throw new BadRequestException('Invalid start_date format');
        }
        if (filters.end_date && isNaN(Date.parse(filters.end_date))) {
            throw new BadRequestException('Invalid end_date format');
        }

        // Simulate database query with filtering
        // In a real implementation, this would query a database
        const bookings = Array.from({ length: 3 }, (_, i) => {
            const bookingDate = new Date();
            bookingDate.setDate(bookingDate.getDate() - i); // Spread bookings over past days

            // Apply basic filtering logic
            if (filters.start_date && bookingDate < new Date(filters.start_date)) {
                return null;
            }
            if (filters.end_date && bookingDate > new Date(filters.end_date)) {
                return null;
            }

            return {
                id: `booking-${i + 1}`,
                status: 'confirmed',
                rider: {
                    id: `user-${i + 1}`,
                    first_name: `User${i + 1}`,
                    last_name: 'Doe',
                    avatar_url: `https://example.com/avatars/user-${i + 1}.jpg`
                },
                partner_avatar_url: `https://example.com/partners/partner-${i + 1}.jpg`,
                number_of_tickets: Math.floor(Math.random() * 4) + 1,
                total_amount: Math.floor(Math.random() * 500) + 200,
                booking_reference: `KWANSO-${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
                arrival_time: new Date(bookingDate.getTime() + 2 * 60 * 60 * 1000).toISOString(),
                departure_time: bookingDate.toISOString(),
                route: {
                    origin: {
                        name: 'Downtown Station',
                        address: '123 Main St, City',
                        lat: 40.7128,
                        lng: -74.0060
                    },
                    destination: {
                        name: 'Airport Terminal',
                        address: '456 Airport Rd, City',
                        lat: 40.6413,
                        lng: -73.7781
                    }
                },
                pickup_stop: {
                    name: 'Downtown Station',
                    address: '123 Main St, City',
                    lat: 40.7128,
                    lng: -74.0060
                },
                drop_off_stop: {
                    name: 'Airport Terminal',
                    address: '456 Airport Rd, City',
                    lat: 40.6413,
                    lng: -73.7781
                },
                booking_schedule_date: bookingDate.toISOString()
            };
        });

        // Simulate async database call
        await new Promise(resolve => setTimeout(resolve, 1000));

        return c.json(bookings);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

interface Schedule {
    id: string;
    departure_time: string;
    status: string;
    booked_seats?: number;
    route?: {
        origin: string;
        destination: string;
        base_price: number;
    };
    shuttle?: {
        name: string;
        capacity: number;
    };
}

// Get today's schedules
organizationRoute.get('/schedules/today', async (c: Context) => {
    const user = c.get('user');

    // Extract query parameters
    const filters = {
        route_id: c.req.query('route_id'), // Optional filter for specific route
    };

    try {
        // Validate query parameters
        if (filters.route_id && !/^[a-zA-Z0-9-]+$/.test(filters.route_id)) {
            throw new BadRequestException('Invalid route_id format');
        }

        // Simulate database query for today's schedules
        // In a real implementation, this would query a database for schedules with today's date
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Normalize to start of day

        const schedules: Schedule[] = Array.from({ length: 4 }, (_, i) => {
            const departureTime = new Date(today.getTime() + (i + 8) * 60 * 60 * 1000); // Schedules starting from 8 AM

            // Apply basic filtering logic
            const routeId = filters.route_id || `route-${Math.floor(Math.random() * 2) + 1}`;
            if (filters.route_id && routeId !== filters.route_id) {
                return null;
            }

            return {
                id: `schedule-${i + 1}`,
                departure_time: departureTime.toISOString(),
                status: ['Scheduled', 'Boarding', 'Departed'][Math.floor(Math.random() * 3)],
                booked_seats: Math.floor(Math.random() * 20) + 5,
                route: {
                    origin: 'Downtown Station',
                    destination: 'Airport Terminal',
                    base_price: Math.floor(Math.random() * 100) + 50
                },
                shuttle: {
                    name: `Shuttle ${String.fromCharCode(65 + i)}`, // e.g., Shuttle A, B, C, D
                    capacity: 30
                }
            };
        }).filter((schedule): schedule is any => schedule !== null);

        // Simulate async database call
        await new Promise(resolve => setTimeout(resolve, 1000));

        return c.json(schedules);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

interface Customer {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    avatar_url?: string;
    created_at: string;
}

// Get recent customers
organizationRoute.get('/customers/recent', async (c: Context) => {
    const user = c.get('user');

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
    };

    try {
        // Validate query parameters
        if (filters.start_date && isNaN(Date.parse(filters.start_date))) {
            throw new BadRequestException('Invalid start_date format');
        }
        if (filters.end_date && isNaN(Date.parse(filters.end_date))) {
            throw new BadRequestException('Invalid end_date format');
        }

        // Simulate database query for recent customers
        const customers: Customer[] = Array.from({ length: 5 }, (_, i) => {
            const createdDate = new Date();
            createdDate.setDate(createdDate.getDate() - i * 2); // Spread customers over past days

            // Apply basic filtering logic
            if (filters.start_date && createdDate < new Date(filters.start_date)) {
                return null;
            }
            if (filters.end_date && createdDate > new Date(filters.end_date)) {
                return null;
            }

            return {
                id: `customer-${i + 1}`,
                first_name: `User${i + 1}`,
                last_name: ['Smith', 'Johnson', 'Brown', 'Taylor', 'Wilson'][i],
                email: `user${i + 1}@example.com`,
                phone_number: `+234${Math.floor(100000000 + Math.random() * 900000000)}`,
                avatar_url: i % 2 === 0 ? `https://example.com/avatars/customer-${i + 1}.jpg` : undefined,
                created_at: createdDate.toISOString(),
            };
        }).filter((customer): customer is any => customer !== null);

        // Simulate async database call
        await new Promise(resolve => setTimeout(resolve, 1000));

        return c.json(customers);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});


export default organizationRoute;
