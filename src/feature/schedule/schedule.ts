import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import { ScheduleService } from './service/schedule.service';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';
import { BadRequestException } from '../../core/exception';
import { RouteService } from '../route/service/route.service';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { BookingService } from '../booking/service/booking.service';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const scheduleRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
scheduleRoute.use('*', authMiddleware);

// Supabase middleware
scheduleRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})


// Create a new schedule (Admin and Partner only)
scheduleRoute.post('/', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const scheduleService = new ScheduleService(c.get('supabase'));
    const data = await c.req.json();
    
    // Validate required fields
    if (!data.route_id || !data.shuttle_id || !data.departure_time || !data.arrival_time || !data.price) {
        throw new BadRequestException('Missing required fields: route_id, shuttle_id, departure_time, arrival_time, and price are required');
    }

    // Validate data types
    if (typeof data.route_id !== 'string' || 
        typeof data.shuttle_id !== 'string' || 
        typeof data.price !== 'number' || 
        data.price <= 0) {
        throw new BadRequestException('Invalid data types or values: route_id and shuttle_id must be strings, price must be a positive number');
    }

    // Validate departure and arrival times
    const departureTime = new Date(data.departure_time);
    const arrivalTime = new Date(data.arrival_time);
    
    if (isNaN(departureTime.getTime()) || isNaN(arrivalTime.getTime())) {
        throw new BadRequestException('Invalid date format for departure_time or arrival_time');
    }

    if (departureTime >= arrivalTime) {
        throw new BadRequestException('Departure time must be before arrival time');
    }

    if (departureTime <= new Date()) {
        throw new BadRequestException('Departure time must be in the future');
    }
    
    // If user is a partner, ensure they can only create schedules for their routes
    const user = c.get('user');
    if (user.role === Role.USER) {
        const routeService = new RouteService(c.get('supabase'));
        const route = await routeService.findOne(data.route_id);
        if (route.partner_id !== user.orgId) {
            throw new BadRequestException('You can only create schedules for your own routes');
        }
    }

    const schedule = await scheduleService.create(data);
    return c.json({
        status: 'success',
        data: schedule,
        message: 'Schedule created successfully'
    }, 201);
});

// Get all schedules with filters
scheduleRoute.get('/', async (c: Context) => {
    const scheduleService = new ScheduleService(c.get('supabase'));
    
    // Extract query parameters
    const filters = {
        route_id: c.req.query('route_id'),
        shuttle_id: c.req.query('shuttle_id'),
        from_date: c.req.query('from_date'),
        to_date: c.req.query('to_date'),
        status: c.req.query('status')
    };
    
    const schedules = await scheduleService.findAll(filters);
    return c.json(schedules);
});

// Get a specific schedule
scheduleRoute.get('/:id', roleGuard([Role.ADMIN, Role.USER, Role.DEVICE]), async (c: Context) => {
    const scheduleService = new ScheduleService(c.get('supabase'));
    const schedule = await scheduleService.findOne(c.req.param('id'));
    return c.json(schedule);
});

// Get a specific schedule route stops meta
scheduleRoute.get('/:id/stops/meta',async (c: Context) => {
    const scheduleService = new ScheduleService(c.get('supabase'));
    const scheduleStopsMeta = await scheduleService.findScheduleRouteStopsMeta(c.req.param('id'));

    const bookingService = new BookingService(c.get('supabase'));
    return c.json(scheduleStopsMeta);
});

// Update a schedule
scheduleRoute.put('/:id', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const scheduleService = new ScheduleService(c.get('supabase'));
    const data = await c.req.json();
    
    // Validate time updates if provided
    if (data.departure_time && data.arrival_time) {
        const departureTime = new Date(data.departure_time);
        const arrivalTime = new Date(data.arrival_time);
        
        if (departureTime >= arrivalTime) {
            throw new BadRequestException('Departure time must be before arrival time');
        }

        if (departureTime <= new Date()) {
            throw new BadRequestException('Departure time must be in the future');
        }
    }
    
    const schedule = await scheduleService.update(c.req.param('id'), data);
    return c.json(schedule);
});

// Delete a schedule
scheduleRoute.delete('/:id', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const scheduleService = new ScheduleService(c.get('supabase'));
    await scheduleService.delete(c.req.param('id'));
    return c.json({ message: 'Schedule deleted successfully' }, 200);
});

export default scheduleRoute;