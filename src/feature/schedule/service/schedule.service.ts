import { SupabaseClient } from '@supabase/supabase-js';
import { Schedule, CreateScheduleDto, UpdateScheduleDto } from '../interface/schedule.interface';
import { BadRequestException } from '../../../core/exception';

export class ScheduleService {
    constructor(private readonly supabase: SupabaseClient) {}

    async create(createScheduleDto: CreateScheduleDto): Promise<Schedule> {
        // Validate shuttle capacity and availability
        const { data: shuttle, error: shuttleError } = await this.supabase
            .from('shuttles')
            .select('capacity')
            .eq('id', createScheduleDto.shuttle_id)
            .single();

        if (shuttleError || !shuttle) {
            throw new BadRequestException('Invalid shuttle');
        }

        // Set initial available seats to shuttle capacity
        createScheduleDto.available_seats = shuttle.capacity;

        const { data, error } = await this.supabase
            .from('schedules')
            .insert([createScheduleDto])
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async findAll(filters?: {
        route_id?: string;
        shuttle_id?: string;
        from_date?: string;
        to_date?: string;
        status?: string;
    }): Promise<Schedule[]> {
        let query = this.supabase
            .from('schedules')
            .select('*, route_id(*), shuttle_id(*)')
            .order('departure_time', { ascending: true });

        if (filters?.route_id) {
            query = query.eq('route_id', filters.route_id);
        }

        if (filters?.shuttle_id) {
            query = query.eq('shuttle_id', filters.shuttle_id);
        }

        if (filters?.status) {
            query = query.eq('status', filters.status);
        }

        if (filters?.from_date) {
            query = query.gte('departure_time', filters.from_date);
        }

        if (filters?.to_date) {
            query = query.lte('arrival_time', filters.to_date);
        }

        const { data, error } = await query;

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }

    async findOne(id: string): Promise<Schedule> {
        const { data, error } = await this.supabase
            .from('schedules')
            .select('*, routes(*), shuttles(*)')
            .eq('id', id)
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async update(id: string, updateScheduleDto: UpdateScheduleDto): Promise<Schedule> {
        const { data, error } = await this.supabase
            .from('schedules')
            .update(updateScheduleDto)
            .eq('id', id)
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async delete(id: string): Promise<void> {
        // Check if there are any bookings for this schedule
        const { data: bookings, error: bookingError } = await this.supabase
            .from('bookings')
            .select('id')
            .eq('schedule_id', id)
            .limit(1);

        if (bookingError) {
            throw new BadRequestException(bookingError.message);
        }

        if (bookings && bookings.length > 0) {
            throw new BadRequestException('Cannot delete schedule with existing bookings');
        }

        const { error } = await this.supabase
            .from('schedules')
            .delete()
            .eq('id', id);

        if (error) {
            throw new BadRequestException(error.message);
        }
    }

    async updateAvailableSeats(id: string, seatsToDecrease: number): Promise<void> {
        const { error } = await this.supabase.rpc('update_schedule_seats', {
            schedule_id: id,
            seats_count: seatsToDecrease
        });

        if (error) {
            throw new BadRequestException(error.message);
        }
    }

    async findScheduleRouteStopsMeta(id: string): Promise<any[]> {
        const { data, error } = await this.supabase
            .from('schedule_stops')
            .select(`
                *,
                status(id,description)
            `)
            .eq('schedule_id', id)

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }
}