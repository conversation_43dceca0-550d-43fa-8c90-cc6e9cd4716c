export interface Schedule {
    id: string;
    route_id: string;
    shuttle_id: string;
    driver_id?: string;
    departure_time: string;
    arrival_time: string;
    available_seats: number;
    price: number;
    status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
    created_at?: string;
    updated_at?: string;
}

export interface CreateScheduleDto {
    route_id: string;
    shuttle_id: string;
    driver_id?: string;
    departure_time: string;
    arrival_time: string;
    available_seats: number;
    price: number;
}

export interface UpdateScheduleDto {
    departure_time?: string;
    arrival_time?: string;
    available_seats?: number;
    price?: number;
    driver_id?: string;
    status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
}