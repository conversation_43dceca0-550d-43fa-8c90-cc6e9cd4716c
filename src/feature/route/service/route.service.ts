import { SupabaseClient } from '@supabase/supabase-js';
import { Route, CreateRouteDto, UpdateRouteDto } from '../interface/route.interface';
import { BadRequestException } from '../../../core/exception';

export class RouteService {
    constructor(private readonly supabase: SupabaseClient) {}

    async create(createRouteDto: CreateRouteDto): Promise<Route> {
        const { data, error } = await this.supabase
            .from('routes')
            .insert([createRouteDto])
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async findAll(filters?: { page?: number; limit?: number; search?: string }): Promise<{ data: Route[]; count: number }> {
        if (!this.supabase) {
            throw new BadRequestException('Database client not initialized');
        }

        // Set default pagination values
        const page = filters?.page || 1;
        const limit = filters?.limit || 10;
        const offset = (page - 1) * limit;

        let query = this.supabase
            .from('routes')
            .select(`*,
                origin(id, name, longitude, latitude),
                destination(id, name, longitude, latitude)
                `,
                { count: 'exact' })
            .order('created_at', { ascending: false });

        // Apply search if provided
        if (filters?.search) {
            query = query.or(`name.ilike.%${filters.search}%,start_location.ilike.%${filters.search}%,end_location.ilike.%${filters.search}%`);
        }

        // Apply pagination
        if (limit > 0) {
            query = query.range(offset, offset + limit - 1);
        }

        const { data, count, error } = await query;

        if (error) {
            throw new BadRequestException(error.message);
        }

        return { data: data || [], count: count || 0 };
    }

    async findOne(id: string): Promise<Route> {
        const { data, error } = await this.supabase
            .from('routes')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async update(id: string, updateRouteDto: UpdateRouteDto): Promise<Route> {
        const { data, error } = await this.supabase
            .from('routes')
            .update(updateRouteDto)
            .eq('id', id)
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async delete(id: string): Promise<void> {
        const { error } = await this.supabase
            .from('routes')
            .delete()
            .eq('id', id);

        if (error) {
            throw new BadRequestException(error.message);
        }
    }

    async findNearbyStops(latitude: number, longitude: number, radius: number = 5): Promise<any[]> {
        const { data, error } = await this.supabase
            .rpc('get_nearest_routes_stops_by_destination', {
                target_lat: latitude,
                target_lng: longitude,
                radius_km: radius
            });

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }

    async findRouteStops(routeId: string): Promise<any[]> {
        const { data, error } = await this.supabase
            .from('route_stops')
            .select(`
                *,
                route_id(id,name),
                location_id(id,name,longitude,latitude)
            `)
            .eq('route_id', routeId)
            .order('stop_order', { ascending: true });

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }

    async countRoutesForShuttle(shuttleId: string): Promise<number> {
        // Count routes assigned to a shuttle through schedules
        const { count, error } = await this.supabase
            .from('schedules')
            .select('route_id', { count: 'exact', head: true })
            .eq('shuttle_id', shuttleId);

        if (error) {
            throw new BadRequestException(error.message);
        }

        return count || 0;
    }

}