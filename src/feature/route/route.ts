import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import { RouteService } from './service/route.service';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';
import { BadRequestException } from '../../core/exception';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const routeRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
routeRoute.use('*', authMiddleware);

// Supabase middleware
routeRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Create a new route (Admin and Partner only)
routeRoute.post('/', async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    const data = await c.req.json();

    // Validate required fields
    if (!data.name || !data.start_location || !data.end_location) {
        throw new BadRequestException('Missing required fields: name, start_location, and end_location are required');
    }

    // Validate data types
    if (typeof data.name !== 'string' || typeof data.start_location !== 'string' || typeof data.end_location !== 'string') {
        throw new BadRequestException('Invalid data types: name, start_location, and end_location must be strings');
    }

    // If user is a partner, ensure they can only create routes for their organization
    const user = c.get('user');
    if (user.role === Role.USER) {
        data.partner_id = user.orgId;
    }

    const route = await routeService.create(data);
    return c.json({
        status: 'success',
        data: route,
        message: 'Route created successfully'
    }, 201);
});

// Get all routes (Admin can see all, Partner can see their own)
routeRoute.get('/', async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    const user = c.get('user');

    try {
        const page = c.req.query('page');
        const limit = c.req.query('limit');
        const search = c.req.query('search');

        const { data, count } = await routeService.findAll({
            page: page ? parseInt(page, 10) : undefined,
            limit: limit ? parseInt(limit, 10) : undefined,
            search: search || undefined
        });

        return c.json({
            data,
            page: page ? parseInt(page, 10) : 1,
            limit: limit ? parseInt(limit, 10) : 10,
            count
        });
    } catch (error: any) {
        throw new BadRequestException(error.message || 'Error getting routes');
    }
});

// Get a specific route
routeRoute.get('/:id', roleGuard([Role.ADMIN.toString(), Role.USER.toString()]), async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    const route = await routeService.findOne(c.req.param('id'));

    // Check if partner has access to this route
    const user = c.get('user');
    if (user.role === Role.USER && route.partner_id !== user.orgId) {
        return c.json({ message: 'Route not found' }, 404);
    }

    return c.json(route);
});

// Count the number of routes assigned to a shuttle
routeRoute.get('/shuttle/:shuttleId/count', async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    const shuttleId = c.req.param('shuttleId');

    const count = await routeService.countRoutesForShuttle(shuttleId);
    return c.json({ count });
});

// Update a route
routeRoute.put('/:id', roleGuard([Role.ADMIN.toString(), Role.USER.toString()]), async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    const routeId = c.req.param('id');

    // Check if partner has access to this route
    const user = c.get('user');
    if (user.role === Role.USER) {
        const existingRoute = await routeService.findOne(routeId);
        if (existingRoute.partner_id !== user.orgId) {
            return c.json({ message: 'Route not found' }, 404);
        }
    }

    const data = await c.req.json();
    const route = await routeService.update(routeId, data);
    return c.json(route);
});

// Delete a route
routeRoute.delete('/:id', roleGuard([Role.ADMIN.toString(), Role.USER.toString()]), async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    const routeId = c.req.param('id');

    // Check if partner has access to this route
    const user = c.get('user');
    if (user.role === Role.USER) {
        const existingRoute = await routeService.findOne(routeId);
        if (existingRoute.partner_id !== user.orgId) {
            return c.json({ message: 'Route not found' }, 404);
        }
    }

    await routeService.delete(routeId);
    return c.json({ message: 'Route deleted successfully' }, 200);
});

// Get nearby route stops
routeRoute.get('/stops/nearby', async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));

    const latitude = parseFloat(c.req.query('latitude') || '');
    const longitude = parseFloat(c.req.query('longitude') || '');
    const radius = parseFloat(c.req.query('radius') || '5');

    if (isNaN(latitude) || isNaN(longitude)) {
        throw new BadRequestException('Valid latitude and longitude are required');
    }

    try {
        const stops = await routeService.findNearbyStops(latitude, longitude, radius);

        return c.json({
            data: stops,
            count: stops.length
        });
    } catch (error: any) {
        throw new BadRequestException(error.message || 'Error finding nearby stops');
    }
});

// Get a specific route stops
routeRoute.get('/:id/stops', async (c: Context) => {
    const routeService = new RouteService(c.get('supabase'));
    try {
        const routeStops = await routeService.findRouteStops(c.req.param('id'));

        // Get the route to access origin and destination coordinates
        const route = await routeService.findOne(c.req.param('id'));

        // Function to calculate distance between two points using Haversine formula
        const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
            const R = 6371; // Radius of the Earth in km
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a =
                Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distance = R * c; // Distance in km
            return parseFloat(distance.toFixed(2));
        };

        return c.json(routeStops.map(stop => {
            // Calculate distance from origin to this stop
            const distanceFromOrigin = calculateDistance(
                route.origin_location.latitude,
                route.origin_location.longitude,
                stop.location_id.latitude,
                stop.location_id.longitude
            );

            return {
                route_id: stop.route_id.id,
                route_name: stop.route_id.name,
                stop_id: stop.id,
                stop_name: stop.location_id.name,
                stop_latitude: stop.location_id.latitude,
                stop_longitude: stop.location_id.longitude,
                distance_km: distanceFromOrigin
            }
        }));
    } catch (error: any) {
        throw new BadRequestException(error.message || 'Error finding route stops');
    }
});
export default routeRoute;