import { SupabaseClient } from '@supabase/supabase-js';
import { Shuttle, CreateShuttleDto, UpdateShuttleDto } from '../interface/shuttle.interface';
import { BadRequestException } from '../../../core/exception';

export class ShuttleService {
    constructor(private readonly supabase: SupabaseClient) {}

    async create(createShuttleDto: CreateShuttleDto): Promise<Shuttle> {
        const { data, error } = await this.supabase
            .from('shuttles')
            .insert([createShuttleDto])
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async findAll(partnerId?: string): Promise<Shuttle[]> {
        let query = this.supabase
            .from('shuttles')
            .select('*')
            .order('created_at', { ascending: false });

        if (partnerId) {
            query = query.eq('partner_id', partnerId);
        }

        const { data, error } = await query;

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }

    async findOne(id: string): Promise<Shuttle> {
        const { data, error } = await this.supabase
            .from('shuttles')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async update(id: string, updateShuttleDto: UpdateShuttleDto): Promise<Shuttle> {
        const { data, error } = await this.supabase
            .from('shuttles')
            .update(updateShuttleDto)
            .eq('id', id)
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async delete(id: string): Promise<void> {
        const { error } = await this.supabase
            .from('shuttles')
            .delete()
            .eq('id', id);

        if (error) {
            throw new BadRequestException(error.message);
        }
    }
}